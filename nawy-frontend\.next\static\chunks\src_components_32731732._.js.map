{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/nawy-project/nawy-frontend/src/components/NavBar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { Heart, Menu, X } from 'lucide-react';\r\nimport Image from 'next/image';\r\n\r\nexport default function NavBar() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const links = [\r\n    { href: '/', label: 'Home' },\r\n    { href: '/search', label: 'Search' },\r\n    { href: '/sell', label: 'Sell' },\r\n    { href: '/blog', label: 'Blog' },\r\n    { href: '/about', label: 'About' },\r\n    { href: '/contact', label: 'Contact' },\r\n    { href: '/nawynow', label: 'Nawy Now' },\r\n    { href: '/careers', label: 'Careers' },\r\n    { href: '/verify', label: 'Verify Agent' },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"bg-white shadow fixed top-0 left-0 right-0 z-50\">\r\n      <div className=\"container mx-auto flex items-center justify-between p-4\">\r\n        {/* Logo */}\r\n        <Link href=\"/\" className=\"h-8\">\r\n          <Image\r\n            src=\"/favicon-32x32.png\"\r\n            alt=\"Nawy Apartments Logo\"\r\n            height={32}\r\n            width={32}\r\n          />\r\n        </Link>\r\n\r\n        {/* Desktop Nav */}\r\n        <nav className=\"hidden lg:flex space-x-6 items-center\">\r\n          {links.map(link => (\r\n            <Link\r\n              key={link.href}\r\n              href={link.href}\r\n              className=\"text-gray-600 hover:text-blue-600\"\r\n            >\r\n              {link.label}\r\n            </Link>\r\n          ))}\r\n          <Heart className=\"w-6 h-6 text-gray-600 hover:text-red-500\" />\r\n          <button className=\"text-gray-600 hover:text-blue-600\">العربية</button>\r\n        </nav>\r\n\r\n        {/* Mobile Hamburger */}\r\n        <button\r\n          className=\"lg:hidden text-gray-600\"\r\n          onClick={() => setIsOpen(!isOpen)}\r\n          aria-label=\"Toggle menu\"\r\n          aria-expanded={isOpen}\r\n        >\r\n          {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Mobile Menu */}\r\n      {isOpen && (\r\n        <nav className=\"lg:hidden bg-white shadow-md border-t border-gray-200 px-4 pt-2 pb-4 space-y-2\">\r\n          {links.map(link => (\r\n            <Link\r\n              key={link.href}\r\n              href={link.href}\r\n              className=\"block text-gray-700 hover:text-blue-600\"\r\n              onClick={() => setIsOpen(false)} // auto-close on click\r\n            >\r\n              {link.label}\r\n            </Link>\r\n          ))}\r\n          <div className=\"flex items-center gap-4 pt-2 border-t border-gray-100 mt-2\">\r\n            <Heart className=\"w-6 h-6 text-gray-600 hover:text-red-500\" />\r\n            <button className=\"text-gray-600 hover:text-blue-600\">العربية</button>\r\n          </div>\r\n        </nav>\r\n      )}\r\n    </header>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAW;QACtC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAW,OAAO;QAAe;KAC1C;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;0CAOlB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAO,WAAU;0CAAoC;;;;;;;;;;;;kCAIxD,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU,CAAC;wBAC1B,cAAW;wBACX,iBAAe;kCAEd,uBAAS,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;iDAAe,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKzD,wBACC,6LAAC;gBAAI,WAAU;;oBACZ,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,UAAU;sCAExB,KAAK,KAAK;2BALN,KAAK,IAAI;;;;;kCAQlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAO,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;AAMlE;GA3EwB;KAAA", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/nawy-project/nawy-frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\n\r\nexport default function Footer() {\r\n  const year = new Date().getFullYear();\r\n\r\n  return (\r\n    <footer className=\"bg-gray-100 border-t border-gray-200 mt-12 text-gray-600 text-sm\">\r\n      <div className=\"container mx-auto px-4 py-8 grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n        {/* About */}\r\n        <div>\r\n          <h4 className=\"text-base font-semibold mb-2\">About Nawy</h4>\r\n          <p className=\"text-gray-500\">\r\n            Nawy helps you find, sell, and invest in properties across Egypt and beyond.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Quick Links */}\r\n        <div>\r\n          <h4 className=\"text-base font-semibold mb-2\">Quick Links</h4>\r\n          <ul className=\"space-y-1\">\r\n            <li><Link href=\"/search\" className=\"hover:text-blue-600\">Search Properties</Link></li>\r\n            <li><Link href=\"/sell\" className=\"hover:text-blue-600\">Sell Your Property</Link></li>\r\n            <li><Link href=\"/about\" className=\"hover:text-blue-600\">About Us</Link></li>\r\n            <li><Link href=\"/contact\" className=\"hover:text-blue-600\">Contact</Link></li>\r\n            <li><Link href=\"/blog\" className=\"hover:text-blue-600\">Blog</Link></li>\r\n          </ul>\r\n        </div>\r\n\r\n        {/* Legal / Language */}\r\n        <div>\r\n          <h4 className=\"text-base font-semibold mb-2\">More</h4>\r\n          <ul className=\"space-y-1\">\r\n            <li><Link href=\"/careers\" className=\"hover:text-blue-600\">Careers</Link></li>\r\n            <li><Link href=\"/privacy\" className=\"hover:text-blue-600\">Privacy Policy</Link></li>\r\n            <li><button className=\"hover:text-blue-600\">العربية</button></li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-t border-gray-200 py-4 text-center text-xs text-gray-500\">\r\n        © {year} Nawy Properties. All rights reserved.\r\n      </div>\r\n    </footer>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,OAAO,IAAI,OAAO,WAAW;IAEnC,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAC7C,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAC7C,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsB;;;;;;;;;;;kDACzD,6LAAC;kDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAAsB;;;;;;;;;;;kDACvD,6LAAC;kDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsB;;;;;;;;;;;kDACxD,6LAAC;kDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsB;;;;;;;;;;;kDAC1D,6LAAC;kDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;kCAK3D,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAC7C,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsB;;;;;;;;;;;kDAC1D,6LAAC;kDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsB;;;;;;;;;;;kDAC1D,6LAAC;kDAAG,cAAA,6LAAC;4CAAO,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKlD,6LAAC;gBAAI,WAAU;;oBAAkE;oBAC5E;oBAAK;;;;;;;;;;;;;AAIhB;KA1CwB", "debugId": null}}]}