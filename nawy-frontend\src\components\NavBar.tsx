'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Heart, Menu, X } from 'lucide-react';
import Image from 'next/image';

export default function NavBar() {
  const [isOpen, setIsOpen] = useState(false);

  const links = [
    { href: '/', label: 'Home' },
    { href: '/search', label: 'Search' },
    { href: '/sell', label: 'Sell' },
    { href: '/blog', label: 'Blog' },
    { href: '/about', label: 'About' },
    { href: '/contact', label: 'Contact' },
    { href: '/nawynow', label: 'Nawy Now' },
    { href: '/careers', label: 'Careers' },
    { href: '/verify', label: 'Verify Agent' },
  ];

  return (
    <header className="bg-white shadow fixed top-0 left-0 right-0 z-50">
      <div className="container mx-auto flex items-center justify-between p-4">
        {/* Logo */}
        <Link href="/" className="h-8">
          <Image
            src="/favicon-32x32.png"
            alt="Nawy Apartments Logo"
            height={32}
            width={32}
          />
        </Link>

        {/* Desktop Nav */}
        <nav className="hidden lg:flex space-x-6 items-center">
          {links.map(link => (
            <Link
              key={link.href}
              href={link.href}
              className="text-gray-600 hover:text-blue-600"
            >
              {link.label}
            </Link>
          ))}
          <Heart className="w-6 h-6 text-gray-600 hover:text-red-500" />
          <button className="text-gray-600 hover:text-blue-600">العربية</button>
        </nav>

        {/* Mobile Hamburger */}
        <button
          className="lg:hidden text-gray-600"
          onClick={() => setIsOpen(!isOpen)}
          aria-label="Toggle menu"
          aria-expanded={isOpen}
        >
          {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <nav className="lg:hidden bg-white shadow-md border-t border-gray-200 px-4 pt-2 pb-4 space-y-2">
          {links.map(link => (
            <Link
              key={link.href}
              href={link.href}
              className="block text-gray-700 hover:text-blue-600"
              onClick={() => setIsOpen(false)} // auto-close on click
            >
              {link.label}
            </Link>
          ))}
          <div className="flex items-center gap-4 pt-2 border-t border-gray-100 mt-2">
            <Heart className="w-6 h-6 text-gray-600 hover:text-red-500" />
            <button className="text-gray-600 hover:text-blue-600">العربية</button>
          </div>
        </nav>
      )}
    </header>
  );
}