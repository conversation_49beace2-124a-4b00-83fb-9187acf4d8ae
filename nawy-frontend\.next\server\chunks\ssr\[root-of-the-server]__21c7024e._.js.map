{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/nawy-project/nawy-frontend/src/components/HeroSearch.tsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\n\r\nexport default function HeroSearch() {\r\n  const [tab, setTab] = useState<'Compounds'|'Properties'>('Properties');\r\n\r\n  const placeholder = 'Area, Compound, Real Estate Developer';\r\n  const dropdowns = [\r\n    { label: 'Property Types' },\r\n    { label: 'Beds and Baths' },\r\n    { label: 'Price Range' }\r\n  ];\r\n\r\n  return (\r\n    <section\r\n      className=\"relative h-screen bg-cover bg-center\"\r\n      style={{\r\n        backgroundImage: `url('/hero-bg.jpg')` // replace with your hero image\r\n      }}\r\n    >\r\n      <div className=\"absolute inset-0 bg-black/40\" />\r\n      <div className=\"relative z-10 flex flex-col items-center justify-center h-full text-white px-4\">\r\n        <h1 className=\"text-4xl md:text-6xl font-bold mb-2\">YOUR HOME IN A COMPOUND</h1>\r\n        <p className=\"mb-8 text-lg md:text-2xl\">Search And Compare Among 15000+ Properties And 800+ Prime Compounds</p>\r\n\r\n        <div className=\"w-full max-w-4xl bg-white rounded-xl shadow-xl p-6\">\r\n          <div className=\"flex border-b\">\r\n            {['Compounds','Properties'].map(label => (\r\n              <button\r\n                key={label}\r\n                onClick={() => setTab(label as any)}\r\n                className={`flex-1 py-2 text-center font-medium ${tab===label ? 'border-b-4 border-blue-600 text-blue-600' : 'text-gray-600'}`}\r\n              >\r\n                {label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mt-4\">\r\n            <input\r\n              type=\"text\"\r\n              placeholder={placeholder}\r\n              className=\"col-span-2 border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            {dropdowns.map(d => (\r\n              <div key={d.label} className=\"relative\">\r\n                <button className=\"w-full text-left border border-gray-300 rounded p-2 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-blue-500\">\r\n                  {d.label} <ChevronDown className=\"w-4 h-4 text-gray-500\" />\r\n                </button>\r\n              </div>\r\n            ))}\r\n            <button className=\"bg-blue-600 text-white font-semibold rounded p-2 hover:bg-blue-700 transition\">\r\n              Search\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAEzD,MAAM,cAAc;IACpB,MAAM,YAAY;QAChB;YAAE,OAAO;QAAiB;QAC1B;YAAE,OAAO;QAAiB;QAC1B;YAAE,OAAO;QAAc;KACxB;IAED,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB,CAAC,mBAAmB,CAAC,CAAC,+BAA+B;QACxE;;0BAEA,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAA2B;;;;;;kCAExC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAY;iCAAa,CAAC,GAAG,CAAC,CAAA,sBAC9B,8OAAC;wCAEC,SAAS,IAAM,OAAO;wCACtB,WAAW,CAAC,oCAAoC,EAAE,QAAM,QAAQ,6CAA6C,iBAAiB;kDAE7H;uCAJI;;;;;;;;;;0CAQX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAa;wCACb,WAAU;;;;;;oCAEX,UAAU,GAAG,CAAC,CAAA,kBACb,8OAAC;4CAAkB,WAAU;sDAC3B,cAAA,8OAAC;gDAAO,WAAU;;oDACf,EAAE,KAAK;oDAAC;kEAAC,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;2CAF3B,EAAE,KAAK;;;;;kDAMnB,8OAAC;wCAAO,WAAU;kDAAgF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9G", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/nawy-project/nawy-frontend/src/components/SearchFilters.tsx"], "sourcesContent": ["import { ApartmentFilterDto } from '@/dto/dto';\r\nimport { ChangeEvent } from 'react';\r\n\r\ninterface Props {\r\n  filters: ApartmentFilterDto;\r\n  onChange: (e: ChangeEvent<HTMLInputElement>) => void;\r\n}\r\n\r\nexport default function SearchFilters({ filters, onChange }: Props) {\r\n  return (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-2 mb-4\">\r\n      <input\r\n        type=\"text\"\r\n        name=\"unitName\"\r\n        value={filters.unitName}\r\n        onChange={onChange}\r\n        placeholder=\"Unit Name\"\r\n        className=\"border p-2 rounded\"\r\n      />\r\n      <input\r\n        type=\"text\"\r\n        name=\"unitNumber\"\r\n        value={filters.unitNumber}\r\n        onChange={onChange}\r\n        placeholder=\"Unit Number\"\r\n        className=\"border p-2 rounded\"\r\n      />\r\n      <input\r\n        type=\"text\"\r\n        name=\"project\"\r\n        value={filters.project}\r\n        onChange={onChange}\r\n        placeholder=\"Project\"\r\n        className=\"border p-2 rounded\"\r\n      />\r\n      <button className=\"bg-blue-600 text-white p-2 rounded\">Search</button>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAQe,SAAS,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAS;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,MAAK;gBACL,OAAO,QAAQ,QAAQ;gBACvB,UAAU;gBACV,aAAY;gBACZ,WAAU;;;;;;0BAEZ,8OAAC;gBACC,MAAK;gBACL,MAAK;gBACL,OAAO,QAAQ,UAAU;gBACzB,UAAU;gBACV,aAAY;gBACZ,WAAU;;;;;;0BAEZ,8OAAC;gBACC,MAAK;gBACL,MAAK;gBACL,OAAO,QAAQ,OAAO;gBACtB,UAAU;gBACV,aAAY;gBACZ,WAAU;;;;;;0BAEZ,8OAAC;gBAAO,WAAU;0BAAqC;;;;;;;;;;;;AAG7D", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/nawy-project/nawy-frontend/src/components/ApartmentCard.tsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { ApartmentResponseDto } from '../dto/dto';\r\n\r\ninterface Props {\r\n  apartment: ApartmentResponseDto;\r\n  showFull?: boolean;\r\n}\r\n\r\nexport default function ApartmentCard({ apartment, showFull = false }: Props) {\r\n  // Ensure no trailing whitespace in image URL\r\n  const rawUrl = apartment.image || '/placeholder.png';\r\n  const imageUrl = rawUrl.trimEnd();\r\n\r\n  return (\r\n    <Link href={showFull ? '#' : `/apartments/${apartment.id}`}>      \r\n      <div className=\"border rounded overflow-hidden hover:shadow-lg transition-shadow\">\r\n        <Image\r\n          src={imageUrl}\r\n          alt={apartment.title}\r\n          width={400}\r\n          height={300}\r\n          className=\"w-full h-48 object-cover\"\r\n        />\r\n        <div className=\"p-4\">\r\n          <h3 className=\"text-lg font-semibold\">{apartment.title}</h3>\r\n          <p className=\"text-sm text-gray-600\">{apartment.address}</p>\r\n          {showFull ? (\r\n            <dl className=\"mt-2 grid grid-cols-2 gap-2 text-sm\">\r\n              <dt>Price</dt><dd>${apartment.price}</dd>\r\n              <dt>Beds</dt><dd>{apartment.bedsNumber}</dd>\r\n              <dt>Baths</dt><dd>{apartment.bathsNumber}</dd>\r\n              <dt>Project</dt><dd>{apartment.project}</dd>\r\n            </dl>\r\n          ) : (\r\n            <p className=\"mt-2 font-bold\">${apartment.price}</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </Link>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQe,SAAS,cAAc,EAAE,SAAS,EAAE,WAAW,KAAK,EAAS;IAC1E,6CAA6C;IAC7C,MAAM,SAAS,UAAU,KAAK,IAAI;IAClC,MAAM,WAAW,OAAO,OAAO;IAE/B,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,WAAW,MAAM,CAAC,YAAY,EAAE,UAAU,EAAE,EAAE;kBACxD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAK,UAAU,KAAK;oBACpB,OAAO;oBACP,QAAQ;oBACR,WAAU;;;;;;8BAEZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyB,UAAU,KAAK;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAyB,UAAU,OAAO;;;;;;wBACtD,yBACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CAAU,8OAAC;;wCAAG;wCAAE,UAAU,KAAK;;;;;;;8CACnC,8OAAC;8CAAG;;;;;;8CAAS,8OAAC;8CAAI,UAAU,UAAU;;;;;;8CACtC,8OAAC;8CAAG;;;;;;8CAAU,8OAAC;8CAAI,UAAU,WAAW;;;;;;8CACxC,8OAAC;8CAAG;;;;;;8CAAY,8OAAC;8CAAI,UAAU,OAAO;;;;;;;;;;;iDAGxC,8OAAC;4BAAE,WAAU;;gCAAiB;gCAAE,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;AAM3D", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/nawy-project/nawy-frontend/src/hooks/useApartmentsList.tsx"], "sourcesContent": ["'use client';\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport axios from 'axios';\r\nimport { ApartmentResponseDto, ApartmentFilterDto } from '../dto/dto';\r\n\r\nconst API = process.env.NEXT_PUBLIC_API_URL;\r\n\r\nexport default function useApartments(filter: ApartmentFilterDto) {\r\n  const [apartments, setApartments] = useState<ApartmentResponseDto[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchApartments = useCallback(async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const params = new URLSearchParams(\r\n        Object.entries(filter).reduce<Record<string,string>>((acc, [k, v]) => {\r\n          if (v) acc[k] = v;\r\n          return acc;\r\n        }, {})\r\n      ).toString();\r\n      const url = `${API}/api/v1/apartments${params ? `?${params}` : ''}`;\r\n      const res = await axios.get<{ data: ApartmentResponseDto[] }>(url);\r\n      setApartments(res.data.data);\r\n    } catch (e: any) {\r\n      setError(e.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [filter]);\r\n\r\n  useEffect(() => {\r\n    fetchApartments();\r\n  }, [fetchApartments]);\r\n\r\n  return { apartments, loading, error };\r\n}"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;AAKA,MAAM;AAES,SAAS,cAAc,MAA0B;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,SAAS,IAAI,gBACjB,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAwB,CAAC,KAAK,CAAC,GAAG,EAAE;gBAC/D,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG;gBAChB,OAAO;YACT,GAAG,CAAC,IACJ,QAAQ;YACV,MAAM,MAAM,GAAG,IAAI,kBAAkB,EAAE,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,IAAI;YACnE,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAmC;YAC9D,cAAc,IAAI,IAAI,CAAC,IAAI;QAC7B,EAAE,OAAO,GAAQ;YACf,SAAS,EAAE,OAAO;QACpB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAgB;IAEpB,OAAO;QAAE;QAAY;QAAS;IAAM;AACtC", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/nawy-project/nawy-frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport NavBar from '../components/NavBar';\r\nimport HeroSearch from '../components/HeroSearch';\r\nimport { useState, ChangeEvent, useCallback } from 'react';\r\nimport SearchFilters from '../components/SearchFilters';\r\nimport ApartmentCard from '../components/ApartmentCard';\r\nimport useApartments from '../hooks/useApartmentsList';\r\nimport { ApartmentFilterDto } from '../dto/dto';\r\n\r\nexport default function HomePage() {\r\n  const [filters, setFilters] = useState<ApartmentFilterDto>({\r\n    unitName: '',\r\n    unitNumber: '',\r\n    project: '',\r\n  });\r\n\r\n  const onChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFilters(prev => ({ ...prev, [name]: value }));\r\n  }, []);\r\n\r\n  const { apartments, loading, error } = useApartments(filters);\r\n\r\n  return (\r\n    <>\r\n      <NavBar />\r\n      <HeroSearch />\r\n      <main className=\"container mx-auto p-4\">\r\n        <h1 className=\"text-2xl font-bold mb-4\">Apartments</h1>\r\n        <SearchFilters filters={filters} onChange={onChange} />\r\n        {loading && <p>Loading…</p>}\r\n        {error && <p className=\"text-red-600\">Error: {error}</p>}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          {apartments.map(a => (\r\n            <ApartmentCard key={a.id} apartment={a} />\r\n          ))}\r\n        </div>\r\n      </main>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,UAAU;QACV,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IAChD,GAAG,EAAE;IAEL,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAa,AAAD,EAAE;IAErD,qBACE;;0BACE,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC,gIAAA,CAAA,UAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC,mIAAA,CAAA,UAAa;wBAAC,SAAS;wBAAS,UAAU;;;;;;oBAC1C,yBAAW,8OAAC;kCAAE;;;;;;oBACd,uBAAS,8OAAC;wBAAE,WAAU;;4BAAe;4BAAQ;;;;;;;kCAC9C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAA,kBACd,8OAAC,mIAAA,CAAA,UAAa;gCAAY,WAAW;+BAAjB,EAAE,EAAE;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}]}